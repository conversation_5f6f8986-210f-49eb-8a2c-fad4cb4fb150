import logging
from dataclasses import dataclass
from typing import List
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from postgres_runner import PostgreSQLProcedureRunner
from scheduler import execute_procedure_with_connection

logger = logging.getLogger(__name__)


@dataclass
class DatabaseTask:
    procedure: str
    param: str

def execute_database_tasks(tasks: List[DatabaseTask], db_config):

    """Execute database tasks with proper error handling"""
    logger.info(f"Executing {len(tasks)} database tasks...")

    with ThreadPoolExecutor(max_workers=2) as executor:
        # Submit all tasks at once
        future_to_task = {
            executor.submit(
                execute_procedure_with_connection,
                PostgreSQLProcedureRunner(**db_config),
                task.procedure,
                task.param,
                True
            ): task for task in tasks
        }

        for future in as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result()
                logger.info(f"Success: {task.procedure} with param: {task.param}")
            except Exception as e:
                logger.error(f"Failed: {task.procedure} with param: {task.param} - {e}")