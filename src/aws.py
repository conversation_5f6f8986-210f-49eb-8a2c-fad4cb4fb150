import logging
from botocore.exceptions import ClientError
import boto3

def turn_off_instance(cluster_name, service_name, region_name):

    logger = logging.getLogger(__name__)


    try:
        ecs_client = boto3.client('ecs', region_name )
        logger.setLevel(level = logging.INFO )

        logger.info(f"Updating service {service_name} in cluster {cluster_name} to 0 desired count")

        response = ecs_client.update_service(
            cluster=cluster_name,
            service=service_name,
            desiredCount=0,
            forceNewDeployment=True
        )

        logger.info(
            f"Service update initiated successfully. Task ARN: {response.get('service', {}).get('taskDefinition', 'N/A')}")
        return response

    except ClientError as e:
        logger.error(f"Failed to update ECS service: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise