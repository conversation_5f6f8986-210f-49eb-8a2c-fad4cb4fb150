import logging
import os
import threading
from datetime import date, timedelta

from flask import Flask, jsonify

from aws import turn_off_instance
from environment import get_aws_config, get_db_config
from database_task_executor import DatabaseTask, execute_database_tasks
from environment import setup_environment
from batch_processor import PropertyBatchProcessor, get_property_ids_from_database
from postgres_runner import PostgreSQLProcedureRunner

app = Flask(__name__)
logging.getLogger('werkzeug').setLevel(logging.ERROR)


def run_daily_views_and_shutdown():

    current_date = date.today()
    current_date_str = current_date.strftime('%Y_%m')
    seven_days_before_str = (current_date - timedelta(weeks=1)).strftime('%Y_%m')


    task_definitions = {
        'current': [
            DatabaseTask('refresh_materialize_view_concurrently', f'effective_rent_by_{current_date_str}'),
            DatabaseTask('refresh_materialize_view_concurrently', f'rent_listing_by_{current_date_str}'),
            DatabaseTask('refresh_materialize_view_concurrently_without_date_of_record', 'bedroom_future_availability')

        ],
        'previous': [
            DatabaseTask('refresh_materialize_view_concurrently', f'effective_rent_by_{seven_days_before_str}'),
            DatabaseTask('refresh_materialize_view_concurrently', f'rent_listing_by_{seven_days_before_str}'),
        ]
    }

    setup_environment()
    db_config = get_db_config()

    execute_database_tasks(task_definitions['current'], db_config)

    if seven_days_before_str != current_date_str:
        execute_database_tasks(task_definitions['previous'], db_config)

    # Process property IDs batch after database tasks
    process_property_ids_batch(db_config)

    shutdown_instance()


def process_property_ids_batch(db_config):
    """Process property IDs in batches"""
    logger.info("Starting property IDs batch processing...")

    try:
        # Get property IDs from database
        db_runner = PostgreSQLProcedureRunner(**db_config)
        property_ids = get_property_ids_from_database(db_runner)

        if not property_ids:
            logger.warning("No property IDs found to process")
            return

        # Initialize batch processor
        # Puedes configurar el endpoint de API si necesitas enviar a un servicio externo
        api_endpoint = os.getenv("PROPERTY_BATCH_API_ENDPOINT")  # Opcional
        batch_processor = PropertyBatchProcessor(
            api_endpoint=api_endpoint,
            batch_size=int(os.getenv("BATCH_SIZE", "100"))
        )

        # Process in parallel batches
        results = batch_processor.send_batches_parallel(
            property_ids,
            max_workers=int(os.getenv("BATCH_MAX_WORKERS", "3"))
        )

        # Log results
        successful_batches = sum(1 for r in results if r.success)
        total_processed = sum(len(r.property_ids) for r in results if r.success)

        logger.info(f"Batch processing completed: {successful_batches}/{len(results)} batches successful")
        logger.info(f"Total property IDs processed: {total_processed}/{len(property_ids)}")

        # Log any failures
        for result in results:
            if not result.success:
                logger.error(f"Failed batch: {result.message}")

    except Exception as e:
        logger.error(f"Error in property IDs batch processing: {e}")
    finally:
        if 'db_runner' in locals():
            db_runner.close_connection()


def shutdown_instance():
    aws_config = get_aws_config()
    logger.info("Turning off instance...")
    turn_off_instance(**aws_config)


def setup_logging(quiet=False):
    """Configure logging based on mode"""
    level = logging.ERROR if quiet else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


# Configure global logger
logger = setup_logging()


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "ok"}), 200


if __name__ == "__main__":

    task_thread = threading.Thread(target=run_daily_views_and_shutdown)
    task_thread.daemon = True
    task_thread.start()

    app.run(host='0.0.0.0', port=8080, debug=False)