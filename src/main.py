import logging
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import date
from dotenv import load_dotenv
from flask import Flask, jsonify
import threading
from aws import turn_off_instance
from postgres_runner import PostgreSQLProcedureRunner
from scheduler import execute_procedure_with_connection

app = Flask(__name__)


def run_daily_views_and_shutdown():
    current_date = date.today()
    fecha_str = current_date.strftime('%Y_%m')

    env_file = os.path.join(os.path.dirname(__file__), '.env')

    # this is for local environment
    if os.path.exists(env_file):

        load_dotenv(env_file)
        logger.info("Loaded configuration from .env file")
    else:
        logger.info("Using system environment variables")

    procedure_name = "refresh_materialize_view_concurrently"

    param_effective_rent_view = "effective_rent_by_" + fecha_str
    param_rent_listing_view = "rent_listing_by_" + fecha_str

    logger.info(f"Parameters for call 1: {param_effective_rent_view}")
    logger.info(f"Parameters for call 2: {param_rent_listing_view}")

    logger.info("Starting simultaneous procedure execution...")

    db_config = get_db_config()
    with ThreadPoolExecutor(max_workers=2) as executor:
        # Send both tasks to pool
        future1 = executor.submit(
            execute_procedure_with_connection, PostgreSQLProcedureRunner(**db_config), procedure_name,
            param_effective_rent_view, True
        )
        future2 = executor.submit(
            execute_procedure_with_connection,
            PostgreSQLProcedureRunner(**db_config), procedure_name, param_rent_listing_view, True
        )

        # Wait and get results
        for future in as_completed([future1, future2]):
            try:
                result = future.result()
                logger.info(f"Task completed with result: {result}")
            except Exception as e:
                logger.error(f"Error in task: {e}")

    aws_config = get_aws_config()
    turn_off_instance(**aws_config)


logger = setup_logging()

def setup_logging(quiet=False):
    """Configure logging based on mode"""
    level = logging.ERROR if quiet else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


# Configure global logger


def get_db_config():
    db_config = {
        'url': os.getenv("POSTGRES_DB_URL"),
        'user': os.getenv("POSTGRES_DB_USER"),
        'password': os.getenv("POSTGRES_DB_PASSWORD")
    }
    return db_config


def get_aws_config():
    aws_config = {
        'service_name': os.getenv("DD_SERVICE") + "-service",
        'cluster_name': os.getenv("DD_ENV"),
        'region_name':  os.getenv("AWS_REGION")
    }
    return aws_config




@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "ok"}), 200


if __name__ == "__main__":
    task_thread = threading.Thread(target=run_daily_views_and_shutdown)
    task_thread.daemon = True
    task_thread.start()

    app.run(host='0.0.0.0', port=5000, debug=False)