import logging
import os
import json
from typing import List, Dict, Any, Iterator
import requests
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


@dataclass
class BatchResult:
    success: bool
    property_ids: List[int]
    message: str
    response_data: Dict[Any, Any] = None


class PropertyBatchProcessor:
    def __init__(self, api_endpoint: str = None, batch_size: int = 100):
        self.api_endpoint = api_endpoint
        self.batch_size = batch_size

    def send_batch(self, property_ids: List[int], **kwargs) -> BatchResult:
        """
        Send a batch of property IDs to external service or process them
        
        Args:
            property_ids: List of property IDs to process
            **kwargs: Additional parameters for the batch operation
            
        Returns:
            BatchResult with success status and details
        """
        try:
            logger.info(f"Processing batch of {len(property_ids)} property IDs")
            
            if self.api_endpoint:
                return self._send_to_api(property_ids, **kwargs)
            else:
                return self._process_locally(property_ids, **kwargs)
                
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            return BatchResult(
                success=False,
                property_ids=property_ids,
                message=f"Batch processing failed: {str(e)}"
            )

    def _send_to_api(self, property_ids: List[int], **kwargs) -> BatchResult:
        """Send property IDs to external API"""
        try:
            payload = {
                "property_ids": property_ids,
                **kwargs
            }
            
            response = requests.post(
                self.api_endpoint,
                json=payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            
            response.raise_for_status()
            
            return BatchResult(
                success=True,
                property_ids=property_ids,
                message=f"Successfully sent {len(property_ids)} property IDs to API",
                response_data=response.json()
            )
            
        except requests.RequestException as e:
            logger.error(f"API request failed: {e}")
            return BatchResult(
                success=False,
                property_ids=property_ids,
                message=f"API request failed: {str(e)}"
            )

    def _process_locally(self, property_ids: List[int], **kwargs) -> BatchResult:
        """Process property IDs locally (placeholder for your business logic)"""
        logger.info(f"Processing {len(property_ids)} property IDs locally")
        
        # Aquí puedes agregar tu lógica específica
        # Por ejemplo: validar IDs, actualizar base de datos, etc.
        
        return BatchResult(
            success=True,
            property_ids=property_ids,
            message=f"Successfully processed {len(property_ids)} property IDs locally"
        )

    def send_batches_parallel(self, all_property_ids: List[int], max_workers: int = 3, **kwargs) -> List[BatchResult]:
        """
        Send multiple batches in parallel
        
        Args:
            all_property_ids: All property IDs to process
            max_workers: Maximum number of parallel workers
            **kwargs: Additional parameters for batch operations
            
        Returns:
            List of BatchResult objects
        """
        # Split into batches
        batches = [
            all_property_ids[i:i + self.batch_size] 
            for i in range(0, len(all_property_ids), self.batch_size)
        ]
        
        logger.info(f"Processing {len(all_property_ids)} property IDs in {len(batches)} batches")
        
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(self.send_batch, batch, **kwargs): batch 
                for batch in batches
            }
            
            # Collect results
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result.success:
                        logger.info(f"Batch completed: {len(batch)} property IDs")
                    else:
                        logger.error(f"Batch failed: {result.message}")
                        
                except Exception as e:
                    logger.error(f"Batch execution failed: {e}")
                    results.append(BatchResult(
                        success=False,
                        property_ids=batch,
                        message=f"Batch execution failed: {str(e)}"
                    ))
        
        return results


class PropertyFileProducer:
    """Producer que lee property IDs desde archivos en batches"""

    def __init__(self, file_path: str, batch_size: int = 100):
        self.file_path = file_path
        self.batch_size = batch_size
        self.current_position = 0

    def read_batch_generator(self) -> Iterator[List[int]]:
        """
        Generator que lee el archivo en batches de manera eficiente

        Yields:
            List[int]: Batch de property IDs
        """
        try:
            logger.info(f"Starting batch reading from: {self.file_path}")

            with open(self.file_path, 'r') as file:
                batch = []

                for line_num, line in enumerate(file, 1):
                    line = line.strip()

                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue

                    try:
                        # Support different formats
                        if ',' in line:
                            # CSV format: 123,456,789
                            ids_in_line = [int(x.strip()) for x in line.split(',') if x.strip().isdigit()]
                            batch.extend(ids_in_line)
                        elif line.isdigit():
                            # One ID per line
                            batch.append(int(line))
                        else:
                            logger.warning(f"Skipping invalid line {line_num}: {line}")
                            continue

                        # Yield batch when it reaches the desired size
                        if len(batch) >= self.batch_size:
                            logger.info(f"Yielding batch of {len(batch)} property IDs")
                            yield batch.copy()
                            batch.clear()

                    except ValueError as e:
                        logger.warning(f"Error parsing line {line_num}: {line} - {e}")
                        continue

                # Yield remaining items if any
                if batch:
                    logger.info(f"Yielding final batch of {len(batch)} property IDs")
                    yield batch

        except FileNotFoundError:
            logger.error(f"File not found: {self.file_path}")
            return
        except Exception as e:
            logger.error(f"Error reading file: {e}")
            return

    def read_all_batches(self) -> List[List[int]]:
        """
        Lee todos los batches de una vez

        Returns:
            List[List[int]]: Lista de todos los batches
        """
        return list(self.read_batch_generator())

    def process_batches_with_callback(self, callback_func, max_workers: int = 3):
        """
        Procesa cada batch usando una función callback en paralelo

        Args:
            callback_func: Función que recibe un batch y lo procesa
            max_workers: Número máximo de workers paralelos
        """
        batches = self.read_all_batches()

        if not batches:
            logger.warning("No batches to process")
            return []

        logger.info(f"Processing {len(batches)} batches with {max_workers} workers")

        results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(callback_func, batch): batch
                for batch in batches
            }

            # Collect results
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Batch processed successfully: {len(batch)} property IDs")
                except Exception as e:
                    logger.error(f"Error processing batch: {e}")
                    results.append(None)

        return results


def get_property_ids_from_file(file_path: str, batch_size: int = 100) -> List[List[int]]:
    """
    Read property IDs from file in batches

    Args:
        file_path: Path to the file containing property IDs
        batch_size: Size of each batch

    Returns:
        List of batches, where each batch is a list of property IDs
    """
    try:
        property_ids = []

        logger.info(f"Reading property IDs from file: {file_path}")

        with open(file_path, 'r') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()

                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue

                try:
                    # Support different formats
                    if ',' in line:
                        # CSV format: 123,456,789
                        ids_in_line = [int(x.strip()) for x in line.split(',') if x.strip().isdigit()]
                        property_ids.extend(ids_in_line)
                    elif line.isdigit():
                        # One ID per line
                        property_ids.append(int(line))
                    else:
                        logger.warning(f"Skipping invalid line {line_num}: {line}")

                except ValueError as e:
                    logger.warning(f"Error parsing line {line_num}: {line} - {e}")
                    continue

        # Create batches
        batches = [
            property_ids[i:i + batch_size]
            for i in range(0, len(property_ids), batch_size)
        ]

        logger.info(f"Loaded {len(property_ids)} property IDs in {len(batches)} batches from file")
        return batches

    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return []
    except Exception as e:
        logger.error(f"Error reading property IDs from file: {e}")
        return []


def get_property_ids_from_database(db_runner, query: str = None) -> List[int]:
    """
    Get property IDs from database

    Args:
        db_runner: PostgreSQLProcedureRunner instance
        query: Optional custom query, defaults to getting all active property IDs

    Returns:
        List of property IDs
    """
    try:
        if not db_runner.connect():
            logger.error("Could not establish database connection")
            return []

        cursor = db_runner.connection.cursor()

        # Default query if none provided
        if not query:
            query = """
                SELECT DISTINCT property_id
                FROM properties
                WHERE status = 'active'
                ORDER BY property_id
            """

        cursor.execute(query)
        results = cursor.fetchall()

        # Extract property IDs from results
        property_ids = [row[0] for row in results if row[0] is not None]

        logger.info(f"Retrieved {len(property_ids)} property IDs from database")
        return property_ids

    except Exception as e:
        logger.error(f"Error retrieving property IDs: {e}")
        return []
    finally:
        if cursor:
            cursor.close()
