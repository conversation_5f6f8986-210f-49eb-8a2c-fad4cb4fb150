import logging
from typing import List, Dict, Any
import requests
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


@dataclass
class BatchResult:
    success: bool
    property_ids: List[int]
    message: str
    response_data: Dict[Any, Any] = None


class PropertyBatchProcessor:
    def __init__(self, api_endpoint: str = None, batch_size: int = 100):
        self.api_endpoint = api_endpoint
        self.batch_size = batch_size

    def send_batch(self, property_ids: List[int], **kwargs) -> BatchResult:
        """
        Send a batch of property IDs to external service or process them
        
        Args:
            property_ids: List of property IDs to process
            **kwargs: Additional parameters for the batch operation
            
        Returns:
            BatchResult with success status and details
        """
        try:
            logger.info(f"Processing batch of {len(property_ids)} property IDs")
            
            if self.api_endpoint:
                return self._send_to_api(property_ids, **kwargs)
            else:
                return self._process_locally(property_ids, **kwargs)
                
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            return BatchResult(
                success=False,
                property_ids=property_ids,
                message=f"Batch processing failed: {str(e)}"
            )

    def _send_to_api(self, property_ids: List[int], **kwargs) -> BatchResult:
        """Send property IDs to external API"""
        try:
            payload = {
                "property_ids": property_ids,
                **kwargs
            }
            
            response = requests.post(
                self.api_endpoint,
                json=payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            
            response.raise_for_status()
            
            return BatchResult(
                success=True,
                property_ids=property_ids,
                message=f"Successfully sent {len(property_ids)} property IDs to API",
                response_data=response.json()
            )
            
        except requests.RequestException as e:
            logger.error(f"API request failed: {e}")
            return BatchResult(
                success=False,
                property_ids=property_ids,
                message=f"API request failed: {str(e)}"
            )

    def _process_locally(self, property_ids: List[int], **kwargs) -> BatchResult:
        """Process property IDs locally (placeholder for your business logic)"""
        logger.info(f"Processing {len(property_ids)} property IDs locally")
        
        # Aquí puedes agregar tu lógica específica
        # Por ejemplo: validar IDs, actualizar base de datos, etc.
        
        return BatchResult(
            success=True,
            property_ids=property_ids,
            message=f"Successfully processed {len(property_ids)} property IDs locally"
        )

    def send_batches_parallel(self, all_property_ids: List[int], max_workers: int = 3, **kwargs) -> List[BatchResult]:
        """
        Send multiple batches in parallel
        
        Args:
            all_property_ids: All property IDs to process
            max_workers: Maximum number of parallel workers
            **kwargs: Additional parameters for batch operations
            
        Returns:
            List of BatchResult objects
        """
        # Split into batches
        batches = [
            all_property_ids[i:i + self.batch_size] 
            for i in range(0, len(all_property_ids), self.batch_size)
        ]
        
        logger.info(f"Processing {len(all_property_ids)} property IDs in {len(batches)} batches")
        
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(self.send_batch, batch, **kwargs): batch 
                for batch in batches
            }
            
            # Collect results
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result.success:
                        logger.info(f"Batch completed: {len(batch)} property IDs")
                    else:
                        logger.error(f"Batch failed: {result.message}")
                        
                except Exception as e:
                    logger.error(f"Batch execution failed: {e}")
                    results.append(BatchResult(
                        success=False,
                        property_ids=batch,
                        message=f"Batch execution failed: {str(e)}"
                    ))
        
        return results


def get_property_ids_from_database(db_runner, query: str = None) -> List[int]:
    """
    Get property IDs from database
    
    Args:
        db_runner: PostgreSQLProcedureRunner instance
        query: Optional custom query, defaults to getting all active property IDs
        
    Returns:
        List of property IDs
    """
    try:
        if not db_runner.connect():
            logger.error("Could not establish database connection")
            return []
        
        cursor = db_runner.connection.cursor()
        
        # Default query if none provided
        if not query:
            query = """
                SELECT DISTINCT property_id 
                FROM properties 
                WHERE status = 'active' 
                ORDER BY property_id
            """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        # Extract property IDs from results
        property_ids = [row[0] for row in results if row[0] is not None]
        
        logger.info(f"Retrieved {len(property_ids)} property IDs from database")
        return property_ids
        
    except Exception as e:
        logger.error(f"Error retrieving property IDs: {e}")
        return []
    finally:
        if cursor:
            cursor.close()
